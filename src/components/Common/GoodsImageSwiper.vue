<template>
  <div class="goods-image-swiper">
    <swiper :modules="modules" :slides-per-view="slidesPerView" :space-between="spaceBetwee"
      :centered-slides="centeredSlides" :loop="loop" :autoplay="autoplayConfig" :initial-slide="0"
      :loop-additional-slides="1" class="goods-swiper" :class="[`swiper-${props.mode}`]"
      :style="{ height: containerHeight }" @swiper="onSwiper" @slide-change="onSlideChange"
      @real-index-change="onRealIndexChange">
      <swiper-slide v-for="(item, index) in mediaList" :key="index" class="goods-slide">
        <div class="media-container">
          <!-- 图片 -->
          <div v-if="item.type === 'image'" class="image-wrapper">
            <!-- 图片加载状态 -->
            <div v-if="!imageLoadedStates[index]" class="image-loading">
              <div class="loading-spinner"></div>
            </div>
            <img :src="item.url" :alt="item.alt || `商品图片${index + 1}`" class="goods-image"
              :class="[getImageClass(item, index), { 'image-loaded': imageLoadedStates[index] }]"
              @click="handleImageClick(item, index)" @error="handleImageError" @load="handleImageLoad($event, index)" />
          </div>

          <!-- 视频容器 -->
          <div v-else-if="item.type === 'video'" class="video-container">
            <!-- 只有当前激活的视频才显示 Video.js 播放器 -->
            <div v-if="currentIndex === index" class="video-js-container">
              <video :ref="el => setVideoRef(el)" class="video-js vjs-default-skin goods-video" :poster="item.poster"
                preload="metadata" :muted="true" :playsinline="true">
                <source :src="item.url" type="video/mp4" />
                <p class="vjs-no-js">
                  要查看此视频，请启用 JavaScript，并考虑升级到
                  <a href="https://videojs.com/html5-video-support/" target="_blank">
                    支持HTML5视频的网络浏览器
                  </a>。
                </p>
              </video>
            </div>

            <!-- 非激活状态显示封面图 -->
            <div v-else class="video-poster" :style="{ backgroundImage: `url(${item.poster})` }">
              <div class="video-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                  <path d="M8 5V19L19 12L8 5Z" fill="white" fill-opacity="0.8" />
                </svg>
              </div>
            </div>

            <!-- 自定义播放按钮 -->
            <div class="custom-play-button"
              v-show="currentIndex === index && (!isVideoPlaying || playingVideoIndex !== index)"
              @click="handleCustomPlayClick(index)">
              <div class="play-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M8 5V19L19 12L8 5Z" fill="white" />
                </svg>
              </div>
            </div>

            <!-- 加载状态 -->
            <div class="video-loading" v-show="videoLoading && currentIndex === index">
              <div class="loading-spinner"></div>
              <span>加载中...</span>
            </div>
          </div>
        </div>
      </swiper-slide>
    </swiper>

    <!-- 自定义分页器 -->
    <div v-if="showPagination && mediaList.length > 1" class="pagination-container">
      <!-- 插槽优先 -->
      <slot name="pagination" :currentIndex="currentIndex" :total="mediaList.length" :slideTo="slideTo">
        <!-- 默认分页器 -->
        <div v-if="paginationType === 'dots'" class="custom-pagination dots-pagination"
          :class="[`pagination-${props.mode}`]">
          <span v-for="(_, index) in mediaList" :key="index" class="pagination-dot"
            :class="{ active: currentIndex === index }" @click="slideTo(index)"></span>
        </div>

        <!-- 分数指示器 -->
        <div v-else-if="paginationType === 'fraction'" class="custom-pagination fraction-pagination"
          :class="[`pagination-${props.mode}`]">
          <span class="fraction-text">{{ currentIndex + 1 }} / {{ mediaList.length }}</span>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay } from 'swiper/modules'
import videojs from 'video.js'

// 导入样式
import 'swiper/css'
import 'swiper/css/autoplay'
import 'video.js/dist/video-js.css'

// Props
const props = defineProps({
  mediaList: {
    type: Array,
    default: () => []
  },
  autoplay: {
    type: Boolean,
    default: false
  },
  loop: {
    type: Boolean,
    default: true
  },
  // 显示模式 - 'detail' 详情页模式, 'banner' 首页长条模式
  mode: {
    type: String,
    default: 'detail',
    validator: (value) => ['detail', 'banner'].includes(value)
  },
  // 自定义高度
  height: {
    type: [String, Number],
    default: null
  },
  // 分页器类型 - 'dots' 圆点指示器, 'fraction' 分数指示器, 'none' 不显示
  paginationType: {
    type: String,
    default: 'dots',
    validator: (value) => ['dots', 'fraction', 'none'].includes(value)
  },
  // 是否显示分页器
  showPagination: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['imageClick', 'videoClick', 'slideChange'])

// 响应式数据
const swiperInstance = ref(null)
const currentIndex = ref(0)
const screenWidth = ref(window.innerWidth)
const imageAspectRatios = ref({}) // 存储图片宽高比
const imageLoadedStates = ref({}) // 存储图片加载状态
const isVideoPlaying = ref(false) // 视频播放状态
const videoElement = ref(null) // 单个视频元素引用
const videoPlayer = ref(null) // 单个 Video.js 播放器实例
const videoLoading = ref(false) // 视频加载状态
const isSlideChanging = ref(false) // 是否正在切换幻灯片
const playingVideoIndex = ref(-1) // 当前播放的视频索引
const currentVideoItem = ref(null) // 当前视频项
const isPlayerInitialized = ref(false) // 播放器是否已初始化

// Swiper 模块
const modules = [Autoplay]

// 响应式配置
const slidesPerView = computed(() => {
  // 桌面端显示多张
  if (screenWidth.value >= 768) {
    return 2.2
  }
  return 1
})

const spaceBetwee = computed(() => {
  return screenWidth.value >= 768 ? 12 : 0
})

const centeredSlides = computed(() => {
  return screenWidth.value >= 768
})

// 计算容器高度
const containerHeight = computed(() => {
  // 如果有自定义高度，优先使用
  if (props.height) {
    return typeof props.height === 'number' ? `${props.height}px` : props.height
  }

  // 根据模式设置默认高度
  if (props.mode === 'banner') {
    // 长条模式：使用 auto 高度，让图片决定容器高度
    return 'auto'
  }

  // 详情页模式：原有高度
  if (screenWidth.value >= 768) {
    return '400px'
  }
  return '375px'
})

// 自动播放配置
const autoplayConfig = computed(() => {
  if (props.autoplay && props.mediaList.length > 1 && !isVideoPlaying.value) {
    return {
      delay: 1500,
      disableOnInteraction: false,
      pauseOnMouseEnter: true
    }
  }
  return false
})

// Video.js 配置选项
const getVideoJsOptions = () => {
  return {
    controls: true,
    responsive: true,
    fluid: true,
    playbackRates: [0.5, 1, 1.25, 1.5, 2],
    muted: true, // 默认静音以避免自动播放限制
    autoplay: true, // 禁用自动播放
    preload: 'metadata',
    bigPlayButton: false, // 禁用默认的大播放按钮
    techOrder: ['html5'],
    html5: {
      vhs: {
        overrideNative: true
      },
      nativeVideoTracks: false,
      nativeAudioTracks: false,
      nativeTextTracks: false
    }
  }
}

// 设置视频元素引用
const setVideoRef = (el) => {
  if (el) {
    // 检查元素是否已经有播放器实例
    if (el.player) {
      console.warn('视频元素已有播放器实例，跳过初始化')
      return
    }

    videoElement.value = el
    // 延迟初始化播放器，确保元素已挂载
    nextTick(() => {
      if (!isPlayerInitialized.value && !el.player) {
        initVideoPlayer()
      }
    })
  }
}

// 监听当前索引变化，更新视频源
watch(currentIndex, async (newIndex, oldIndex) => {
  if (newIndex === oldIndex) return

  const currentItem = props.mediaList[newIndex]
  if (currentItem && currentItem.type === 'video') {
    currentVideoItem.value = currentItem
    // 等待 DOM 更新后再处理视频
    await nextTick()
    await updateVideoSource(currentItem)
  } else {
    currentVideoItem.value = null
    if (videoPlayer.value && !videoPlayer.value.isDisposed()) {
      videoPlayer.value.pause()
      isVideoPlaying.value = false
      playingVideoIndex.value = -1
    }
  }
})
const onRealIndexChange = (swiper) => {
  if (props.loop && swiper.realIndex !== undefined) {
    const newIndex = swiper.realIndex
    if (newIndex !== currentIndex.value && newIndex >= 0 && newIndex < props.mediaList.length) {
      currentIndex.value = newIndex
    }
  }
}
// 更新视频源
const updateVideoSource = async (videoItem) => {
  // 如果播放器未初始化且有视频元素，先初始化
  if (!isPlayerInitialized.value && videoElement.value) {
    await initVideoPlayer()
  }

  if (videoPlayer.value && !videoPlayer.value.isDisposed()) {
    try {
      // 暂停当前播放
      if (!videoPlayer.value.paused()) {
        videoPlayer.value.pause()
      }

      // 更新视频源
      videoPlayer.value.src({
        src: videoItem.url,
        type: 'video/mp4'
      })

      // 更新封面图
      if (videoItem.poster) {
        videoPlayer.value.poster(videoItem.poster)
      }

      // 重置播放进度
      videoPlayer.value.currentTime(0)

      // 重置状态
      isVideoPlaying.value = false
      playingVideoIndex.value = -1

      console.log(`更新视频源: ${videoItem.url}`)

    } catch (error) {
      console.error('更新视频源失败:', error)
    }
  }
}

// 初始化 Video.js 播放器（只初始化一次）
const initVideoPlayer = async () => {
  if (isPlayerInitialized.value || !videoElement.value) {
    return
  }

  // 双重检查：确保元素没有已存在的播放器
  if (videoElement.value.player) {
    console.warn('视频元素已有播放器实例，取消初始化')
    videoPlayer.value = videoElement.value.player
    isPlayerInitialized.value = true
    return
  }


  try {
    // 确保元素存在且已挂载到 DOM
    if (!videoElement.value.parentNode) {
      console.warn('视频元素尚未挂载到 DOM，延迟初始化')
      setTimeout(() => initVideoPlayer(), 100)
      return
    }

    // 设置初始化标志，防止并发初始化
    isPlayerInitialized.value = true

    const options = getVideoJsOptions()

    videoPlayer.value = videojs(videoElement.value, options, function () {
      console.log('Video.js 播放器初始化完成（单例模式）')
    })

    // 绑定事件 - 使用防抖处理
    let playTimeout = null
    let pauseTimeout = null

    videoPlayer.value.on('play', () => {
      clearTimeout(pauseTimeout)
      playTimeout = setTimeout(() => {
        if (!isSlideChanging.value) {
          handleVideoPlay()
        }
      }, 50)
    })

    videoPlayer.value.on('pause', () => {
      clearTimeout(playTimeout)
      pauseTimeout = setTimeout(() => {
        if (!isSlideChanging.value) {
          handleVideoPause()
        }
      }, 50)
    })

    videoPlayer.value.on('ended', () => handleVideoEnded())
    videoPlayer.value.on('loadeddata', () => handleVideoLoaded())
    videoPlayer.value.on('error', (error) => handleVideoError(error))

    videoPlayer.value.on('waiting', () => {
      videoLoading.value = true
    })

    videoPlayer.value.on('canplay', () => {
      videoLoading.value = false
    })

    // 设置响应式
    videoPlayer.value.responsive(true)

  } catch (error) {
    console.error('初始化 Video.js 播放器失败:', error)
    isPlayerInitialized.value = false
  }
}

// 方法
const onSwiper = (swiper) => {
  swiperInstance.value = swiper
}
const onSlideChange = (swiper) => {
  isSlideChanging.value = true

  // 修复索引计算逻辑
  let newIndex
  if (props.loop) {
    // 循环模式下优先使用 realIndex，但需要处理边界情况
    newIndex = swiper.realIndex !== undefined ? swiper.realIndex : swiper.activeIndex
    // 确保索引在有效范围内
    if (newIndex < 0) {
      newIndex = props.mediaList.length - 1
    } else if (newIndex >= props.mediaList.length) {
      newIndex = 0
    }
  } else {
    // 非循环模式直接使用 activeIndex
    newIndex = swiper.activeIndex
  }

  // 如果索引没有变化，直接返回
  if (newIndex === currentIndex.value) {
    isSlideChanging.value = false
    return
  }

  console.log(`Slide change: ${currentIndex.value} -> ${newIndex}, realIndex: ${swiper.realIndex}, activeIndex: ${swiper.activeIndex}`)

  currentIndex.value = newIndex

  setTimeout(() => {
    isSlideChanging.value = false
  }, 100)

  emit('slideChange', currentIndex.value)
}

const slideTo = (index) => {
  if (swiperInstance.value) {
    // 确保索引在有效范围内
    const targetIndex = Math.max(0, Math.min(index, props.mediaList.length - 1))

    if (props.loop) {
      // 循环模式下使用 slideToLoop
      swiperInstance.value.slideToLoop(targetIndex)
    } else {
      // 非循环模式使用 slideTo
      swiperInstance.value.slideTo(targetIndex)
    }
  }
}

const handleImageClick = (item, index) => {
  emit('imageClick', { item, index })
}

// 自定义播放按钮点击处理
const handleCustomPlayClick = async (index) => {
  if (index !== currentIndex.value) {
    return
  }

  // 确保播放器已初始化
  if (!isPlayerInitialized.value) {
    await initVideoPlayer()
  }

  if (!videoPlayer.value || videoPlayer.value.isDisposed()) {
    console.warn('视频播放器未准备好')
    return
  }

  try {
    videoLoading.value = true

    // 确保视频从头开始播放
    videoPlayer.value.currentTime(0)

    // 开始播放
    await videoPlayer.value.play()
    console.log(`开始播放视频 ${index}`)

  } catch (error) {
    console.error('视频播放失败:', error)
    videoLoading.value = false

    // 处理自动播放被阻止的情况
    if (error.name === 'NotAllowedError') {
      alert('浏览器阻止了视频自动播放，请手动点击播放按钮')
    }
  }
}

const handleVideoPlay = () => {
  console.log(`视频播放事件触发`)
  isVideoPlaying.value = true
  playingVideoIndex.value = currentIndex.value
  videoLoading.value = false

  // 停止轮播
  if (swiperInstance.value && swiperInstance.value.autoplay) {
    swiperInstance.value.autoplay.stop()
  }

  emit('videoClick', { item: currentVideoItem.value, index: currentIndex.value })
}

const handleVideoPause = () => {
  // 如果正在切换幻灯片，忽略暂停事件
  if (isSlideChanging.value) {
    return
  }

  console.log(`视频暂停事件触发`)
  isVideoPlaying.value = false
  playingVideoIndex.value = -1

  // 恢复轮播
  if (swiperInstance.value && swiperInstance.value.autoplay && props.autoplay) {
    swiperInstance.value.autoplay.start()
  }
}

const handleVideoEnded = () => {
  console.log(`视频播放结束`)
  isVideoPlaying.value = false
  playingVideoIndex.value = -1

  // 视频结束后重置到开始位置
  if (videoPlayer.value && !videoPlayer.value.isDisposed()) {
    videoPlayer.value.currentTime(0)
  }

  // 恢复轮播
  if (swiperInstance.value && swiperInstance.value.autoplay && props.autoplay) {
    swiperInstance.value.autoplay.start()
  }
}

const handleVideoLoaded = () => {
  console.log(`视频加载完成`)
  videoLoading.value = false
}

const handleVideoError = (error) => {
  console.error(`视频加载错误:`, error)
  videoLoading.value = false
}

const handleImageError = (event) => {
  console.warn('图片加载失败:', event.target.src)
}

const handleImageLoad = (event, index) => {
  const img = event.target
  const aspectRatio = img.naturalWidth / img.naturalHeight
  imageAspectRatios.value[index] = aspectRatio
  imageLoadedStates.value[index] = true
}

const getImageClass = (item, index) => {
  const aspectRatio = imageAspectRatios.value[index]
  if (!aspectRatio) return ''

  // 详情页模式逻辑
  if (screenWidth.value < 768) {
    return aspectRatio > 1.2 ? 'image-landscape' : aspectRatio < 0.8 ? 'image-portrait' : 'image-square'
  } else {
    return 'image-desktop'
  }
}

// 窗口大小变化监听
const handleResize = () => {
  screenWidth.value = window.innerWidth
  if (swiperInstance.value) {
    swiperInstance.value.update()
  }

  // 更新视频播放器的响应式
  if (videoPlayer.value && !videoPlayer.value.isDisposed()) {
    videoPlayer.value.responsive(true)
  }
}

// 预加载图片 - 优先加载第一张图片
const preloadImages = () => {
  const imageItems = props.mediaList.filter(item => item.type === 'image')

  if (imageItems.length === 0) return

  // 优先加载第一张图片
  const loadImage = (item, index, priority = false) => {
    const img = new Image()

    img.onload = () => {
      const aspectRatio = img.naturalWidth / img.naturalHeight
      imageAspectRatios.value[index] = aspectRatio
      imageLoadedStates.value[index] = true
    }

    img.onerror = () => {
      imageLoadedStates.value[index] = false
    }

    // 设置图片优先级
    if (priority) {
      img.loading = 'eager'
      img.fetchPriority = 'high'
    }

    img.src = item.url
  }

  // 先加载第一张图片（高优先级）
  props.mediaList.forEach((item, index) => {
    if (item.type === 'image') {
      loadImage(item, index, index === 0)
    }
  })
}

// 在 onMounted 中确保正确的初始索引
onMounted(() => {
  window.addEventListener('resize', handleResize)

  // 预加载图片
  preloadImages()

  // 确保初始索引正确
  nextTick(() => {
    if (swiperInstance.value) {
      const initialIndex = props.loop ? swiperInstance.value.realIndex : swiperInstance.value.activeIndex
      if (initialIndex !== undefined && initialIndex !== currentIndex.value) {
        currentIndex.value = initialIndex
      }
    }
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)

  // 清理播放器
  if (videoPlayer.value && !videoPlayer.value.isDisposed()) {
    videoPlayer.value.dispose()
    videoPlayer.value = null
  }

  // 清理视频元素的播放器引用
  if (videoElement.value && videoElement.value.player) {
    delete videoElement.value.player
  }

  isPlayerInitialized.value = false
  videoElement.value = null
})
</script>

<style scoped lang="less">
.goods-image-swiper {
  position: relative;
  width: 100%;
  background-color: #ffffff;

  .goods-swiper {
    width: 100%;
    // 高度通过内联样式动态设置
  }



  .goods-slide {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;

    @media (min-width: 768px) {
      transform: scale(0.9);
      opacity: 0.8;

      &.swiper-slide-active {
        transform: scale(1);
        opacity: 1;
      }
    }
  }

  .media-container {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
  }



  .image-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .image-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 2;

      .loading-spinner {
        width: 24px;
        height: 24px;
        border: 2px solid rgba(0, 0, 0, 0.1);
        border-top: 2px solid #ff7a0a;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }

  .goods-image {
    cursor: pointer;
    transition: all 0.3s ease;
    display: block;
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
    opacity: 0;

    &.image-loaded {
      opacity: 1;
    }

    &:hover {
      @media (min-width: 768px) {
        transform: scale(1.05);
      }
    }
  }

  .video-container {
    position: relative;
    width: 100%;
    height: 100%;

    .video-js-container {
      width: 100%;
      height: 100%;
    }

    .goods-video {
      width: 100% !important;
      height: 100% !important;
      object-fit: cover;
    }

    // 视频封面图（非激活状态）
    .video-poster {
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #000;

      .video-icon {
        opacity: 0.8;
        transition: opacity 0.3s ease;
      }

      &:hover .video-icon {
        opacity: 1;
      }
    }

    // 自定义播放按钮
    .custom-play-button {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 80px;
      height: 80px;
      background: rgba(0, 0, 0, 0.7);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      z-index: 10;

      &:hover {
        background: rgba(0, 0, 0, 0.8);
        transform: translate(-50%, -50%) scale(1.1);
      }

      .play-icon {
        margin-left: 4px; // 视觉上居中
        transition: transform 0.2s ease;
      }

      &:active .play-icon {
        transform: scale(0.9);
      }
    }

    // 加载状态
    .video-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      color: white;
      font-size: 14px;
      z-index: 15;

      .loading-spinner {
        width: 24px;
        height: 24px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }

  // 分页器容器
  .pagination-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    pointer-events: none; // 让容器不阻挡点击事件
  }

  // 自定义分页器
  .custom-pagination {
    pointer-events: auto; // 恢复分页器的点击事件

    // 圆点分页器 - 底部居中
    &.dots-pagination {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 6px;
      padding: 8px 20px 12px;

      .pagination-dot {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.3);
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          background-color: #ff7a0a;
          transform: scale(1.3);
        }

        &:hover {
          background-color: rgba(255, 122, 10, 0.7);
        }
      }
    }

    // 分数分页器 - 右下角定位
    &.fraction-pagination {
      position: absolute;
      bottom: 12px;
      right: 12px;
      padding: 0;

      .fraction-text {
        font-size: 13px;
        font-weight: 500;
        color: #ffffff;
        background: rgba(0, 0, 0, 0.6);
        padding: 6px 12px;
        border-radius: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(8px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;

        &:hover {
          background: rgba(0, 0, 0, 0.75);
          transform: translateY(-1px);
        }
      }
    }
  }
}

// 旋转动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// Video.js 样式自定义
:deep(.video-js) {

  // 隐藏默认的大播放按钮
  .vjs-big-play-button {
    display: none !important;
  }

  .vjs-control-bar {
    background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
  }

  .vjs-poster {
    background-size: cover;
  }

  // 确保控制栏在自定义按钮下方
  .vjs-control-bar {
    z-index: 5;
  }
}

// 响应式优化
@media (max-width: 767px) {
  .goods-image-swiper {
    .media-container {
      border-radius: 0;
      margin: 0;
    }

    // 移动端分数分页器样式调整
    .custom-pagination.fraction-pagination {
      bottom: 8px;
      right: 8px;

      .fraction-text {
        font-size: 12px;
        padding: 4px 8px;
        border-radius: 12px;
      }
    }

    .goods-image {
      &.image-landscape {
        width: 100%;
        height: auto;
        max-height: 100%;
        object-fit: contain;
      }

      &.image-portrait {
        height: 100%;
        width: auto;
        max-width: 100%;
        object-fit: contain;
      }

      &.image-square {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .video-container .custom-play-button {
      width: 60px;
      height: 60px;

      svg {
        width: 20px;
        height: 20px;
      }
    }
  }
}

@media (min-width: 768px) {
  .goods-image-swiper {
    padding: 16px;
    box-sizing: border-box;



    .goods-image {
      &.image-desktop {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }

      &.image-landscape {
        width: 100%;
        height: auto;
        max-height: 100%;
      }

      &.image-portrait {
        height: 100%;
        width: auto;
        max-width: 100%;
      }

      &.image-square {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }


    }
  }


}
</style>
